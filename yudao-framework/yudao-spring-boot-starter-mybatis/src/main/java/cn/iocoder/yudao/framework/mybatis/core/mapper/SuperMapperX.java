package cn.iocoder.yudao.framework.mybatis.core.mapper;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.SortablePageParam;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseIdDO;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;

/**
 * 拓展 Mapper 接口，用于 CRUD 增删改查
 *
 * <AUTHOR>
 */
public interface SuperMapperX<T extends BaseIdDO> extends BaseMapperX<T>{
    default PageResult<T> selectPage(SortablePageParam pageParam) {
        LambdaQueryWrapperX<T> query = new LambdaQueryWrapperX<T>();

        // 如果没有排序字段，则默认按照ID倒序排序
        if (pageParam.getSortingFields() == null || pageParam.getSortingFields().isEmpty()) {
            query.orderByAsc(T::getId);
        }

        return selectPage(pageParam, query);
    }
}

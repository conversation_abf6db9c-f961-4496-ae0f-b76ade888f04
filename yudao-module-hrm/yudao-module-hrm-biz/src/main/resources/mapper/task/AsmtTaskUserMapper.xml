<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.hrm.dal.mysql.task.AsmtTaskUserMapper">
    <select id="searchPage" resultType="cn.iocoder.yudao.module.hrm.controller.admin.task.vo.AsmtTaskUserRespVO">
        SELECT a.*, b.username,b.nickname,b.dept_id,b.email,b.mobile,b.sex,b.`status` AS user_status,
               c.`name` AS dept_name, d.task_name, d.begin_ts,d.end_ts,d.pkg_id, d.status as task_status
        FROM hrm_asmt_task_user a
        LEFT JOIN system_users b ON a.user_id=b.id
        LEFT JOIN system_dept c ON b.dept_id=c.id
        LEFT JOIN hrm_asmt_task d ON a.task_id=d.id
        <where>
            a.deleted=0
            <if test="reqVO.taskId != null">
                AND a.task_id=#{reqVO.taskId}
            </if>
            <if test="reqVO.userId != null">
                AND a.user_id=#{reqVO.userId}
            </if>
            <if test="reqVO.taskStatus != null">
                AND d.`status`=#{reqVO.taskStatus}
            </if>
            <if test="reqVO.status != null">
                AND a.`status`=#{reqVO.status}
            </if>
            <if test="reqVO.keyword != null and reqVO.keyword !=''">
                AND (
                    b.mobile LIKE CONCAT('%', #{reqVO.keyword}, '%')
                    OR b.email LIKE CONCAT('%', #{reqVO.keyword}, '%')
                    OR b.nickname LIKE CONCAT('%', #{reqVO.keyword}, '%')
                )
            </if>
        </where>
        ORDER BY d.begin_ts, d.end_ts
        LIMIT #{reqVO.pageNo}, #{reqVO.pageSize}
    </select>

    <select id="searchCount" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM hrm_asmt_task_user a
        LEFT JOIN system_users b ON a.user_id=b.id
        LEFT JOIN system_dept c ON b.dept_id=c.id
        LEFT JOIN hrm_asmt_task d ON a.task_id=d.id
        <where>
            a.deleted=0
            <if test="reqVO.taskId != null">
                AND a.task_id=#{reqVO.taskId}
            </if>
            <if test="reqVO.userId != null">
                AND a.user_id=#{reqVO.userId}
            </if>
            <if test="reqVO.taskStatus != null">
                AND d.`status`=#{reqVO.taskStatus}
            </if>
            <if test="reqVO.status != null">
                AND a.`status`=#{reqVO.status}
            </if>
            <if test="reqVO.keyword != null and reqVO.keyword !=''">
                AND (
                b.mobile LIKE CONCAT('%', #{reqVO.keyword}, '%')
                OR b.email LIKE CONCAT('%', #{reqVO.keyword}, '%')
                OR b.nickname LIKE CONCAT('%', #{reqVO.keyword}, '%')
                )
            </if>
        </where>
    </select>
</mapper>

package cn.iocoder.yudao.module.hrm.controller.admin.task.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测评任务 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AsmtTaskRespVO {

    private Long id;

    @Schema(description = "任务名称", example = "张三")
    @ExcelProperty("任务名称")
    private String taskName;

    @Schema(description = "开始时间")
    @ExcelProperty("开始时间")
    private LocalDateTime beginTs;

    @Schema(description = "结束时间")
    @ExcelProperty("结束时间")
    private LocalDateTime endTs;

    /**
     * 测评包id
     */
    private Long pkgId;

    private String pkgName;
    /**
     * 测评报告模板id
     */
    private Long reportTplId;

    @Schema(description = "开放报告")
    @ExcelProperty(value = "开放报告", converter = DictConvert.class)
    @DictFormat("infra_boolean_string") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Boolean openReport;

    @Schema(description = "任务描述", example = "随便")
    @ExcelProperty("任务描述")
    private String description;

    @Schema(description = "标签", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("标签")
    private String tags;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "状态", example = "2")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat("common_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    Long userNum; // 关联的用户数
    Long answerNum; // 提交评测用户数
}

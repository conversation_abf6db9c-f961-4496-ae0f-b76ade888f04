package cn.iocoder.yudao.module.hrm.controller.admin.stdscore.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 指标原始分转标准分区间设置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AsmtIndexStdscoreRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "指标编码", example = "A")
    @ExcelProperty("指标编码")
    private String indexCode;

    @Schema(description = "原始分下区间")
    @ExcelProperty("原始分下区间")
    private Integer rawMin;

    @Schema(description = "原始分上区间")
    @ExcelProperty("原始分上区间")
    private Integer rawMax;

    @Schema(description = "对应标准分")
    @ExcelProperty("对应标准分")
    private Integer stdScore;

}
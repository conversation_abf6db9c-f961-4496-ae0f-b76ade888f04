package cn.iocoder.yudao.module.hrm.controller.admin.indexresult;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.hrm.controller.admin.indexresult.vo.AsmtIndexResultPageReqVO;
import cn.iocoder.yudao.module.hrm.controller.admin.indexresult.vo.AsmtIndexResultRespVO;
import cn.iocoder.yudao.module.hrm.controller.admin.indexresult.vo.AsmtIndexResultSaveReqVO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.indexresult.AsmtIndexResultDO;
import cn.iocoder.yudao.module.hrm.service.indexresult.AsmtIndexResultService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 测评指标计算结果")
@RestController
@RequestMapping("/hrm/asmt-index-result")
@Validated
public class AsmtIndexResultController {

    @Resource
    private AsmtIndexResultService asmtIndexResultService;

    @GetMapping("/get")
    @Operation(summary = "获得测评指标计算结果")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-index-result:query')")
    public CommonResult<AsmtIndexResultRespVO> getAsmtIndexResult(@RequestParam("id") Long id) {
        AsmtIndexResultDO asmtIndexResult = asmtIndexResultService.getAsmtIndexResult(id);
        return success(BeanUtils.toBean(asmtIndexResult, AsmtIndexResultRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得测评指标计算结果分页")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-index-result:query')")
    public CommonResult<PageResult<AsmtIndexResultRespVO>> getAsmtIndexResultPage(@Valid AsmtIndexResultPageReqVO pageReqVO) {
        PageResult<AsmtIndexResultRespVO> pageResult = asmtIndexResultService.getAsmtIndexResultPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出测评指标计算结果 Excel")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-index-result:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAsmtIndexResultExcel(@Valid AsmtIndexResultPageReqVO pageReqVO,
                                           HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AsmtIndexResultRespVO> list = asmtIndexResultService.getAsmtIndexResultPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "测评指标计算结果.xls", "数据", AsmtIndexResultRespVO.class,
                BeanUtils.toBean(list, AsmtIndexResultRespVO.class));
    }

    @GetMapping("/map-by-user")
    @Operation(summary = "以map格式返回指定用户的某个模型的所有指标")
    @Parameters({
            @Parameter(name = "userId", description = "用户ID", example = "1"),
            @Parameter(name = "taskId", description = "任务ID", example = "1")
    })
    public CommonResult<Map<String, Object>> getUserIndexResultMap(@RequestParam Long userId,
                                                                   @RequestParam Long taskId) throws Exception {
        return success(asmtIndexResultService.getUserIndexResultMap(taskId, null, userId, false));
    }

//    @GetMapping("/map-by-user/jm")
//    @Operation(summary = "以map格式返回指定用户的某个模型的所有指标(适用积木报表，无嵌套对象)")
//    @Parameters({
//            @Parameter(name = "modelId", description = "模型ID", example = "1"),
//            @Parameter(name = "userId", description = "用户ID", example = "1"),
//            @Parameter(name = "taskId", description = "任务ID", example = "1")
//    })
//    public CommonResult<Map<String, Object>> getUserIndexResultMapJm(@RequestParam Long userId,
//                                                                     @RequestParam Long taskId,
//                                                                     @RequestParam Long modelId) throws Exception {
//        return success(asmtIndexResultService.getUserIndexResultMap(taskId, modelId, userId, true));
//    }


    @PostMapping("/calc-index-result")
    @Operation(summary = "计算用户测评结果")
    @PreAuthorize("@ss.hasPermission('hhrm:asmt-index-result:calc')")
    public CommonResult<Object> calcIndexResult(@RequestParam("taskId") Long taskId, @RequestParam("userId") Long userId) {
        return success(asmtIndexResultService.calcIndexResult(taskId, userId));
    }


    @GetMapping("/export")
    @Operation(summary = "导出测评指标计算结果 Excel")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-index-result:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportExcel(@RequestParam(required = false) Long taskId,
                            @RequestParam(required = false) Long userId,
                                           HttpServletResponse response) throws IOException {
        asmtIndexResultService.exportTableDataByTaskUser(taskId, userId, response);
    }
}

package cn.iocoder.yudao.module.hrm.controller.admin.paper;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.hrm.controller.admin.paper.vo.AsmtPaperQuestionPageReqVO;
import cn.iocoder.yudao.module.hrm.controller.admin.paper.vo.AsmtPaperQuestionRespVO;
import cn.iocoder.yudao.module.hrm.controller.admin.paper.vo.AsmtPaperQuestionSaveReqVO;
import cn.iocoder.yudao.module.hrm.controller.admin.paper.vo.AsmtPaperQuestionSortReqVO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.paper.AsmtPaperQuestionDO;
import cn.iocoder.yudao.module.hrm.service.paper.AsmtPaperQuestionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@RestController
@RequestMapping("/hrm/asmt-paper-question")
@Validated
public class AsmtPaperQuestionController {

    @Resource
    private AsmtPaperQuestionService asmtPaperQuestionService;

    @PostMapping("/create")
    @Operation(summary = "创建测评卷与测评题关联")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-paper-question:create')")
    public CommonResult<Long> createAsmtPaperQuestion(@Valid @RequestBody AsmtPaperQuestionSaveReqVO createReqVO) {
        return success(asmtPaperQuestionService.createAsmtPaperQuestion(createReqVO));
    }

    @PostMapping("/create-batch")
    @Operation(summary = "批量创建测评卷与测评题关联")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-paper-question:create')")
    public CommonResult<?> createAsmtPaperQuestion(@Valid @RequestBody List<AsmtPaperQuestionSaveReqVO> list) {
        asmtPaperQuestionService.createAsmtPaperQuestionBatch(list);
        return success(true);
    }

    @PutMapping("/update")
    @Operation(summary = "更新测评卷与测评题关联")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-paper-question:update')")
    public CommonResult<Boolean> updateAsmtPaperQuestion(@Valid @RequestBody AsmtPaperQuestionSaveReqVO updateReqVO) {
        asmtPaperQuestionService.updateAsmtPaperQuestion(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-sort")
    @Operation(summary = "更新测评卷与测评题关联")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-paper-question:update')")
    public CommonResult<Boolean> updateAsmtPaperQuestionSort(@Valid @RequestBody List<AsmtPaperQuestionSortReqVO> list) {
        asmtPaperQuestionService.updateAsmtPaperQuestionSort(list);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除测评卷与测评题关联")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hrm:asmt-paper-question:delete')")
    public CommonResult<Boolean> deleteAsmtPaperQuestion(@RequestParam("id") Long id) {
        asmtPaperQuestionService.deleteAsmtPaperQuestion(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得测评卷与测评题关联")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-paper-question:query')")
    public CommonResult<AsmtPaperQuestionRespVO> getAsmtPaperQuestion(@RequestParam("id") Long id) {
        AsmtPaperQuestionDO asmtPaperQuestion = asmtPaperQuestionService.getAsmtPaperQuestion(id);
        return success(BeanUtils.toBean(asmtPaperQuestion, AsmtPaperQuestionRespVO.class));
    }

    @GetMapping("/list-by-paper-id")
    @Operation(summary = "根据测评卷ID获得测评卷与测评题关联列表")
    @Parameter(name = "paperId", description = "测评卷ID", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-paper-question:query')")
    public CommonResult<List<AsmtPaperQuestionRespVO>> getAsmtPaperQuestionByPaperId(@RequestParam("paperId") Long paperId) {
        return success(asmtPaperQuestionService.getAsmtPaperQuestionByPaperId(paperId));
    }

    @GetMapping("/get-questions-options")
    @Operation(summary = "根据测评卷ID获得测评卷与测评题关联列表")
    @Parameter(name = "paperId", description = "测评卷ID", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('hrm:asmt-paper-question:query')") TODO 这个权限怎么配置的
    public CommonResult<Object> getPaperQuestionAndOptionsByPaperId(@RequestParam("paperId") Long paperId) {
        return success(asmtPaperQuestionService.getPaperQuestionAndOptionsByPaperId(paperId));
    }

    @GetMapping("/page")
    @Operation(summary = "获得测评卷与测评题关联分页")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-paper-question:query')")
    public CommonResult<PageResult<AsmtPaperQuestionRespVO>> getAsmtPaperQuestionPage(@Valid AsmtPaperQuestionPageReqVO pageReqVO) {
        PageResult<AsmtPaperQuestionDO> pageResult = asmtPaperQuestionService.getAsmtPaperQuestionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AsmtPaperQuestionRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出测评卷与测评题关联 Excel")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-paper-question:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAsmtPaperQuestionExcel(@Valid AsmtPaperQuestionPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AsmtPaperQuestionDO> list = asmtPaperQuestionService.getAsmtPaperQuestionPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "测评卷与测评题关联.xls", "数据", AsmtPaperQuestionRespVO.class,
                        BeanUtils.toBean(list, AsmtPaperQuestionRespVO.class));
    }

}

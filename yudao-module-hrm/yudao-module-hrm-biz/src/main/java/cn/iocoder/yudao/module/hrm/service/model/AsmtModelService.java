package cn.iocoder.yudao.module.hrm.service.model;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.hrm.controller.admin.model.vo.*;
import cn.iocoder.yudao.module.hrm.dal.dataobject.model.AsmtIndexDO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.model.AsmtModelDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 测评模型 Service 接口
 *
 * <AUTHOR>
 */
public interface AsmtModelService {

    /**
     * 创建测评模型
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAsmtModel(@Valid AsmtModelSaveReqVO createReqVO);

    /**
     * 更新测评模型
     *
     * @param updateReqVO 更新信息
     */
    void updateAsmtModel(@Valid AsmtModelSaveReqVO updateReqVO);
    void copyAsmtModel(Long modelId);
    void copyIndexFromPaper(Long modelId);

    /**
     * 删除测评模型
     *
     * @param id 编号
     */
    void deleteAsmtModel(Long id);

    /**
     * 获得测评模型
     *
     * @param id 编号
     * @return 测评模型
     */
    AsmtModelDO getAsmtModel(Long id);
    AsmtModelDO getValidModelByPkgId(Long pkgId);

    /**
     * 获得测评模型关联的评测包所有试卷及其题目
     * @param id 模型ID
     * @return 结果
     */
    Object getModelOriginalIndex(Long id);

    /**
     * 获得测评模型分页
     *
     * @param pageReqVO 分页查询
     * @return 测评模型分页
     */
    PageResult<AsmtModelDO> getAsmtModelPage(AsmtModelPageReqVO pageReqVO);

    // ==================== 子表（测评指标） ====================

    /**
     * 根据模型ID获取已有的指标组名
     * @param modelId 模型ID
     * @return 列表
     */
    List<Object> getIndexGroupsByModelIdOrPaperId(Long modelId, Long paperId);

    /**
     * 获得测评指标分页
     *
     * @param pageReqVO 分页查询
     * @return 测评指标分页
     */
    PageResult<AsmtIndexDO> getAsmtIndexPage(AsmtIndexPageReqVO pageReqVO);

    /** 获取所有索引 */
    List<AsmtIndexDO> getIndexList(AsmtIndexListReqVO pageReqVO);

    /**
     * 获取指定指标可使用的指标
     * @param id 指标ID
     * @return 结果
     */
    Object getCanUseIndex(Long id);

    /**
     * 创建测评指标
     *
     * @param asmtIndex 创建信息
     * @return 编号
     */
    Long createAsmtIndex(@Valid AsmtIndexDO asmtIndex);

    /**
     * 更新测评指标
     *
     * @param asmtIndex 更新信息
     */
    void updateAsmtIndex(@Valid AsmtIndexDO asmtIndex);

    /**
     * 更新指标规则
     * @param asmtIndex AsmtIndexDO，仅需要ID和rule字段
     */
    void updateIndexRule(AsmtIndexDO asmtIndex);

    /**
     * 校验指标公式
     * @param asmtIndex AsmtIndexDO，仅需要ID和rule字段
     */
    String validateIndexRule(AsmtIndexDO asmtIndex);

    /**
     * 删除测评指标
     *
     * @param id 编号
     */
    void deleteAsmtIndex(Long id);

	/**
	 * 获得测评指标
	 *
	 * @param id 编号
     * @return 测评指标
	 */
    AsmtIndexDO getAsmtIndex(Long id);

    Object importIndexList(List<IndexImportExcelVO> list, Long modelId, Long taskId, Long userId);
    void updateIndexSort(List<AsmtIndexDO> list);

    /**
     * 根据评测包ID获取计算模型
     * @param pkgId
     * @return
     */
    AsmtModelDO getAsmtModelByPkgId(Long pkgId);

    /**
     * 根据模型ID获取所有指标列表
     * @param modelId
     * @return
     */
    List<AsmtIndexDO> getIndexList(Long modelId);

    /**
     * 根据模型ID获取计算指标列表
     * @param modelId
     * @return
     */
    List<AsmtIndexDO> getCalcIndexList(Long modelId);
}

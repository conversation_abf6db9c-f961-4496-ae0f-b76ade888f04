package cn.iocoder.yudao.module.hrm.controller.admin.packages.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 测评包-测评卷关联新增/修改 Request VO")
@Data
public class AsmtPackagePaperSortReqVO {

    @Schema(description = "标签id")
    @NotNull(message = "id不能为空")
    private Long id;

    @Schema(description = "显示顺序")
    @NotNull(message = "显示顺序不能为空")
    private Integer sort;

}

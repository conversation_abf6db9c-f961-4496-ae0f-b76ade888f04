package cn.iocoder.yudao.module.hrm.dal.dataobject.paper;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 测评卷 DO
 *
 * <AUTHOR>
 */
@TableName("hrm_asmt_paper")
@KeySequence("hrm_asmt_paper_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AsmtPaperDO extends BaseDO {

    /**
     * 标签id
     */
    @TableId
    private Long id;
    /**
     * 试卷编码
     */
    private String paperCode;
    /**
     * 试卷名称
     */
    private String paperName;
    /**
     * 测评类型
     *
     * 枚举 {@link TODO hrm_asmt_type 对应的类}
     */
    private String asmtType;
    /**
     * 是否随机题目排序
     *
     * 枚举 {@link TODO infra_boolean_string 对应的类}
     */
    private Boolean quesRandom;
    /**
     * 答题分钟数
     */
    private Integer minutes;
    /**
     * 试卷说明
     */
    private String description;
    /**
     * 标签
     */
    private String tags;
    /**
     * 状态
     *
     * 枚举 {@link TODO common_status 对应的类}
     */
    private Integer status;

    /**
     * 答题限制，0既不能重复进入也不允许超时；1允许超时但不允许重复进入；2可以超时也可以重复进入
     */
    private Long answerLimit;

}

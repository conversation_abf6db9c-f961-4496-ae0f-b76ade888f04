package cn.iocoder.yudao.module.hrm.controller.admin.packages;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.hrm.controller.admin.packages.vo.AsmtPackagePaperSortReqVO;
import cn.iocoder.yudao.module.hrm.service.packages.AsmtPackagePaperService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 测评包-测评卷关联")
@RestController
@RequestMapping("/hrm/asmt-package-paper")
@Validated
public class AsmtPackagePaperController {

    @Resource
    private AsmtPackagePaperService asmtPackagePaperService;

    @PutMapping("/update-sort")
    @Operation(summary = "更新测评卷与测评题关联")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-package-paper:update')")
    public CommonResult<Boolean> updateAsmtPaperQuestionSort(@Valid @RequestBody List<AsmtPackagePaperSortReqVO> list) {
        asmtPackagePaperService.updateAsmtPaperQuestionSort(list);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除测评包-测评卷关联")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hrm:asmt-package-paper:delete')")
    public CommonResult<Boolean> deleteAsmtPackagePaper(@RequestParam("id") Long id) {
        asmtPackagePaperService.deleteAsmtPackagePaper(id);
        return success(true);
    }

    @GetMapping("/list-by-pkg")
    @Operation(summary = "获得测评包-测评卷关联列表")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-package-paper:query')")
    public CommonResult<?> getAsmtPackagePaperPage(@RequestParam Long pkgId) {
        return success(asmtPackagePaperService.getListByPkgId(pkgId));
    }

}

package cn.iocoder.yudao.module.hrm.controller.admin.indexresult.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.apache.commons.compress.archivers.dump.DumpArchiveEntry;

@Schema(description = "管理后台 - 测评指标计算结果新增/修改 Request VO")
@Data
public class AsmtIndexResultSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "22258")
    private Long id;

    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1912")
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @Schema(description = "任务id", example = "18350")
    private Long taskId;

    @Schema(description = "模型id", example = "11746")
    private Long modelId;

    @Schema(description = "指标编码")
    private String indexCode;

    @Schema(description = "指标名称")
    private String indexName;

    @Schema(description = "指标分组")
    private String indexGroup;
    /**
     * 标准分转换方式（0-不需要，1-区间映射表，2-公式）
     */
    @Schema(description = "标准分转换方式")
    private Integer coverType;

    @Schema(description = "指标id", example = "31228")
    private Long indexId;

    @Schema(description = "得分")
    private Double score;

    @Schema(description = "标准分")
    private Double stdScore;

    @Schema(description = "参考值")
    private String refValue;

    @Schema(description = "是否为导入数据")
    private Boolean isImport;

    @Schema(description = "结果描述", example = "你猜")
    private String description;

}

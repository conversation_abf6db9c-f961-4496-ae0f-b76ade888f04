package cn.iocoder.yudao.module.hrm.service.paper;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.hrm.controller.admin.paper.vo.AsmtPaperPageReqVO;
import cn.iocoder.yudao.module.hrm.controller.admin.paper.vo.AsmtPaperSaveReqVO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.paper.AsmtPaperDO;
import cn.iocoder.yudao.module.hrm.dal.mysql.paper.AsmtPaperMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.hrm.enums.ErrorCodeConstants.ASMT_PAPER_NOT_EXISTS;

/**
 * 测评卷 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AsmtPaperServiceImpl implements AsmtPaperService {

    @Resource
    private AsmtPaperMapper asmtPaperMapper;

    @Override
    public Long createAsmtPaper(AsmtPaperSaveReqVO createReqVO) {
        // 插入
        AsmtPaperDO asmtPaper = BeanUtils.toBean(createReqVO, AsmtPaperDO.class);
        asmtPaperMapper.insert(asmtPaper);
        // 返回
        return asmtPaper.getId();
    }

    @Override
    public void updateAsmtPaper(AsmtPaperSaveReqVO updateReqVO) {
        // 校验存在
        validateAsmtPaperExists(updateReqVO.getId());
        // 更新
        AsmtPaperDO updateObj = BeanUtils.toBean(updateReqVO, AsmtPaperDO.class);
        asmtPaperMapper.updateById(updateObj);
    }

    @Override
    public void deleteAsmtPaper(Long id) {
        // 校验存在
        validateAsmtPaperExists(id);
        // 删除
        asmtPaperMapper.deleteById(id);
    }

    private void validateAsmtPaperExists(Long id) {
        if (asmtPaperMapper.selectById(id) == null) {
            throw exception(ASMT_PAPER_NOT_EXISTS);
        }
    }

    @Override
    public AsmtPaperDO getAsmtPaper(Long id) {
        return asmtPaperMapper.selectById(id);
    }

    @Override
    public PageResult<AsmtPaperDO> getAsmtPaperPage(AsmtPaperPageReqVO pageReqVO) {
        return asmtPaperMapper.selectPage(pageReqVO);
    }

}
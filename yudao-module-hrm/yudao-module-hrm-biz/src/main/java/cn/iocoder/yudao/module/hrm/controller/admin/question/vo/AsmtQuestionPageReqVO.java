package cn.iocoder.yudao.module.hrm.controller.admin.question.vo;

import cn.iocoder.yudao.framework.common.pojo.SortablePageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 测评题分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AsmtQuestionPageReqVO extends SortablePageParam {

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "题目编码")
    private String quseCode;

    @Schema(description = "测评类型", example = "1")
    private String asmtType;

    @Schema(description = "题型", example = "1")
    private String quesType;

    @Schema(description = "题目")
    private String title;

    @Schema(description = "标签")
    private String tags;

    @Schema(description = "题目说明")
    private String description;

}
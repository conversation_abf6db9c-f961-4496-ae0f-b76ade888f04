package cn.iocoder.yudao.module.hrm.controller.admin.question.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 测评题新增/修改 Request VO")
@Data
public class AsmtQuestionEditReqVO {

    @Schema(description = "题目")
    @NotNull(message = "题目不能为空")
    private AsmtQuestionSaveReqVO question;

    @Schema(description = "答案选项")
    @NotNull(message = "选项不能为空")
    private List<AsmtQuestionOptionSaveReqVO> options;



}

package cn.iocoder.yudao.module.hrm.service.answersheet;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.hrm.controller.admin.answersheet.vo.*;
import cn.iocoder.yudao.module.hrm.dal.dataobject.answersheet.AsmtAnswersheetDO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.question.AsmtQuestionOptionDO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.task.AsmtTaskUserDO;
import cn.iocoder.yudao.module.hrm.dal.mysql.answersheet.AsmtAnswersheetMapper;
import cn.iocoder.yudao.module.hrm.dal.mysql.question.AsmtQuestionOptionMapper;
import cn.iocoder.yudao.module.hrm.dal.mysql.task.AsmtTaskUserMapper;
import cn.iocoder.yudao.module.hrm.service.asmttaskanswering.AsmtTaskAnsweringService;
import cn.iocoder.yudao.module.hrm.service.indexresult.AsmtIndexResultService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.BAD_REQUEST;
import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.UNAUTHORIZED;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.iocoder.yudao.module.hrm.enums.ErrorCodeConstants.ASMT_ANSWERSHEET_EXISTS;
import static cn.iocoder.yudao.module.hrm.enums.ErrorCodeConstants.ASMT_ANSWERSHEET_NOT_EXISTS;

/**
 * 用户答题卡 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class AsmtAnswersheetServiceImpl implements AsmtAnswersheetService {

    @Resource
    private AsmtAnswersheetMapper asmtAnswersheetMapper;

    @Resource
    private AsmtQuestionOptionMapper questionOptionMapper;

    @Resource
    private AsmtTaskUserMapper asmtTaskUserMapper;

    @Resource
    private AsmtTaskAnsweringService taskAnsweringService;

    @Lazy
    @Autowired
    private AsmtIndexResultService indexResultService;

//    @Override

    /** 答题 */
    @Override
    public void createAsmtAnswersheet(List<AsmtAnswersheetSaveReqVO> list) {
        Long userId = getLoginUserId();
        if (userId == null) {
            throw exception(UNAUTHORIZED);
        }

        if (list == null || list.isEmpty()) {
            throw exception(BAD_REQUEST);
        }

        AsmtAnswersheetSaveReqVO first = list.get(0);
        if (checkUserIsAnswer(userId, first.getUserTaskId(), first.getPkgId(), first.getPaperId())) {
            throw exception(ASMT_ANSWERSHEET_EXISTS);
        }

        // 根据答案ID批量查询答案
        List<Long> answerIds = list.stream().map(AsmtAnswersheetSaveReqVO::getAnswerId).filter(Objects::nonNull).distinct().toList();
        LambdaQueryWrapper<AsmtQuestionOptionDO> wrapper = new LambdaQueryWrapper<AsmtQuestionOptionDO>().in(AsmtQuestionOptionDO::getId, answerIds);
        List<AsmtQuestionOptionDO> optionDOList = questionOptionMapper.selectList(wrapper);
        Map<Long, AsmtQuestionOptionDO> optionDOMap = optionDOList.stream().collect(Collectors.toMap(AsmtQuestionOptionDO::getId, o -> o));
        for (AsmtAnswersheetSaveReqVO reqVO : list) {
            reqVO.setUserId(userId);
            AsmtQuestionOptionDO dto = optionDOMap.get(reqVO.getAnswerId());
            reqVO.setAnswer(dto.getOptionName());
            reqVO.setScore(dto.getScore());
        }

        // 插入
        List<AsmtAnswersheetDO> asmtAnswersheetDOS = BeanUtils.toBean(list, AsmtAnswersheetDO.class);
        asmtAnswersheetMapper.insertBatch(asmtAnswersheetDOS);

        // 移除答题标记
        taskAnsweringService.deleteAsmtTaskAnswering(first.getPaperId(), first.getUserTaskId());

        // 更新用户任务状态
        Long num = asmtAnswersheetMapper.getUserCountByTaskId(first.getTaskId(), first.getUserId());
        if (num != null && num > 0) {
            new LambdaUpdateChainWrapper<>(asmtTaskUserMapper)
                    .eq(AsmtTaskUserDO::getId, first.getUserTaskId())
                    .set(AsmtTaskUserDO::getStatus, 1)
                    .update();

            // 异步执行指标计算，提高响应速度并解耦主流程
            log.info("开始异步计算指标");
            new Thread(() -> {
                try {
                    indexResultService.calcIndexResult(first.getTaskId(), first.getUserId());
                } catch (Exception e) {
                    // TODO 补偿及预警机制
                    // 记录异常信息以便后续排查和补偿处理
                    log.error("异步指标计算出错", e);
                }
            }).start();

        }
    }

    @Override
    public void updateAsmtAnswersheet(AsmtAnswersheetSaveReqVO updateReqVO) {
        // 校验存在
        validateAsmtAnswersheetExists(updateReqVO.getId());
        // 更新
        AsmtAnswersheetDO updateObj = BeanUtils.toBean(updateReqVO, AsmtAnswersheetDO.class);
        asmtAnswersheetMapper.updateById(updateObj);
    }

    @Override
    public void deleteByUserTaskIdAndPaperId(Long userTaskId, Long paperId) {
        asmtAnswersheetMapper.delete(new LambdaQueryWrapper<AsmtAnswersheetDO>()
                .eq(AsmtAnswersheetDO::getUserTaskId, userTaskId)
                .eq(AsmtAnswersheetDO::getPaperId, paperId));

        // 把用户任务改成未完成状态
        new LambdaUpdateChainWrapper<>(asmtTaskUserMapper)
                .eq(AsmtTaskUserDO::getId, userTaskId)
                .set(AsmtTaskUserDO::getStatus, 0)
                .update();
    }

    @Override
    public boolean checkUserIsAnswer(Long userId, Long userTaskId, Long pkgId, Long paperId) {
        LambdaQueryWrapperX<AsmtAnswersheetDO> wrapper = new LambdaQueryWrapperX<AsmtAnswersheetDO>()
                .eq(AsmtAnswersheetDO::getUserId, userId)
                .eq(AsmtAnswersheetDO::getUserTaskId, userTaskId)
                .eq(AsmtAnswersheetDO::getPkgId, pkgId)
                .eq(AsmtAnswersheetDO::getPaperId, paperId);
        Long count = asmtAnswersheetMapper.selectCount(wrapper);
        return count != null && count > 0;
    }

    private void validateAsmtAnswersheetExists(Long id) {
        if (asmtAnswersheetMapper.selectById(id) == null) {
            throw exception(ASMT_ANSWERSHEET_NOT_EXISTS);
        }
    }

    @Override
    public AsmtAnswersheetDO getAsmtAnswersheet(Long id) {
        return asmtAnswersheetMapper.selectById(id);
    }

    @Override
    public PageResult<AsmtAnswersheetDO> getAsmtAnswersheetPage(AsmtAnswersheetPageReqVO pageReqVO) {
        return asmtAnswersheetMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<AsmtAnswersheetSearchRespVO> searchPage(AsmtAnswersheetSearchReqVO reqVO) {
        if (reqVO.getPageNo() > 0) {
            reqVO.setPageNo(reqVO.getPageNo() - 1);
        }
        return new PageResult<>(
                asmtAnswersheetMapper.searchPage(reqVO),
                asmtAnswersheetMapper.searchCount(reqVO)
        );
    }

    @Override
    public List<AsmtAnswersheetRespVO> selectListByUserTaskId(Long userTaskId, Long paperId) {
        return asmtAnswersheetMapper.selectListByUserTaskId(userTaskId, paperId);
    }

    @Override
    public List<AsmtAnswersheetDO> selectListByTaskUser(Long taskId, Long userId) {
        return asmtAnswersheetMapper.selectListByTaskUser(taskId, userId);
    }

}

package cn.iocoder.yudao.module.hrm.controller.admin.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 用户 Excel 导入 VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免用户导入有问题
public class IndexImportExcelVO {

    @ExcelProperty("指标名称")
    private String indexName;

    @ExcelProperty("指标编码")
    private String indexCode;

    @ExcelProperty("分组")
    private String indexGroup;

    @ExcelProperty("得分")
    private Double score;

    @ExcelProperty("标准分")
    private Double stdScore;

    @ExcelProperty("参考值")
    private String refValue;

    @ExcelProperty("指标说明")
    private String description;

}

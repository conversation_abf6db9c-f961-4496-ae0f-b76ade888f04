package cn.iocoder.yudao.module.hrm.controller.admin.taskanswering.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 用户答题卡 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TaskAnsweringRespVO {

    private Long id;
    private LocalDateTime createTime;
    private Long paperId;
    private Long userTaskId;
    private String paperName;
    private String paperCode;
    private String asmtType;
    private Long taskId;
    private String nickname;
    private String positionSeries;
    private String jobTitle;
    private String jobNumber;
    private String jobLevel;
    private Long userId;

}

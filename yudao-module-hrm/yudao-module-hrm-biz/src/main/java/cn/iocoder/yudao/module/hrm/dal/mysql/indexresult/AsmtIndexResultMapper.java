package cn.iocoder.yudao.module.hrm.dal.mysql.indexresult;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.hrm.controller.admin.indexresult.vo.AsmtIndexResultPageReqVO;
import cn.iocoder.yudao.module.hrm.controller.admin.indexresult.vo.IndexResultTableExportResVO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.indexresult.AsmtIndexResultDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 测评指标计算结果 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AsmtIndexResultMapper extends BaseMapperX<AsmtIndexResultDO> {

    default PageResult<AsmtIndexResultDO> selectPage(AsmtIndexResultPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AsmtIndexResultDO>()
                .eqIfPresent(AsmtIndexResultDO::getUserId, reqVO.getUserId())
                .eqIfPresent(AsmtIndexResultDO::getTaskId, reqVO.getTaskId())
                .eqIfPresent(AsmtIndexResultDO::getModelId, reqVO.getModelId())
                .eqIfPresent(AsmtIndexResultDO::getIndexId, reqVO.getIndexId())
                .eqIfPresent(AsmtIndexResultDO::getIsImport, reqVO.getIsImport())
                .eqIfPresent(AsmtIndexResultDO::getDescription, reqVO.getDescription())
                .orderByAsc(AsmtIndexResultDO::getId));
    }

    default AsmtIndexResultDO getByUerIdAndTaskIdAndModelIdAndIndexId(Long userId, Long taskId, Long modelId, Long indexId) {
        return selectOne(
                new LambdaQueryWrapper<AsmtIndexResultDO>()
                        .eq(AsmtIndexResultDO::getUserId, userId)
                        .eq(AsmtIndexResultDO::getTaskId, taskId)
                        .eq(AsmtIndexResultDO::getModelId, modelId)
                        .eq(AsmtIndexResultDO::getIndexId, indexId)
        );
    }

    @Delete("delete from hrm_asmt_index_result where task_id = #{taskId} and user_id = #{userId} and is_import = 0")
    void deleteByTaskIdAndUserId(@Param("taskId") Long taskId, @Param("userId") Long userId);

    default List<AsmtIndexResultDO> selectImportListByTaskUser(Long taskId, Long userId){
        return selectList(
                new LambdaQueryWrapper<AsmtIndexResultDO>()
                        .eq(AsmtIndexResultDO::getTaskId, taskId)
                        .eq(AsmtIndexResultDO::getUserId, userId)
                        .eq(AsmtIndexResultDO::getIsImport, 1)
        );
    }

    /* 测评分析 */

    /**
     * 获取表头
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 表头数组
     */
    @Select("<script>" +
            "SELECT a.user_id,a.task_id, MD5(CONCAT(a.user_id,a.task_id)) AS user_key,a.index_code,a.index_name,a.index_group, a.score, a.std_score, MD5(CONCAT(a.index_code,a.index_name)) AS key_id, " +
            "u.nickname, u.position_series, u.job_level, u.job_number,u.dept_id, u.post_ids, i.sort,i.cover_type " +
            "FROM hrm_asmt_index_result a " +
            "LEFT JOIN system_users u ON a.user_id=u.id " +
            "LEFT JOIN hrm_asmt_index i ON a.index_id=i.id " +
            "WHERE a.deleted=0 " +
            "<if test='taskId != null'> AND task_id = #{taskId} </if>" +
            "<if test='userId != null'> AND user_id = #{userId} </if>" +
            "ORDER BY i.sort " +
            "</script>")
    List<IndexResultTableExportResVO> getExportTableListByTaskUser(@Param("taskId") Long taskId, @Param("userId") Long userId);
}

package cn.iocoder.yudao.module.hrm.controller.admin.model.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 测评模型分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AsmtModelPageReqVO extends PageParam {

    @Schema(description = "模型名称", example = "赵六")
    private String modelName;

    @Schema(description = "测评包id", example = "24722")
    private Long pkgId;

    @Schema(description = "状态", example = "1")
    private Integer status;

}
package cn.iocoder.yudao.module.hrm.controller.admin.model;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.hrm.controller.admin.model.vo.*;
import cn.iocoder.yudao.module.hrm.dal.dataobject.model.AsmtIndexDO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.model.AsmtModelDO;
import cn.iocoder.yudao.module.hrm.service.model.AsmtModelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 测评模型")
@RestController
@RequestMapping("/hrm/asmt-model")
@Validated
public class AsmtModelController {

    @Resource
    private AsmtModelService asmtModelService;

    @PostMapping("/create")
    @Operation(summary = "创建测评模型")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-model:create')")
    public CommonResult<Long> createAsmtModel(@Valid @RequestBody AsmtModelSaveReqVO createReqVO) {
        return success(asmtModelService.createAsmtModel(createReqVO));
    }

    @PutMapping("/copy")
    @Operation(summary = "复制模型")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-model:create')")
    public CommonResult<Boolean> copyAsmtModel(@RequestParam Long modelId) {
        asmtModelService.copyAsmtModel(modelId);
        return success(true);
    }

    @PutMapping("/copy-index-from-paper")
    @Operation(summary = "从关联的试卷中复制指标")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-model:create')")
    public CommonResult<Boolean> copyIndexFromPaper(@RequestParam Long modelId) {
        asmtModelService.copyIndexFromPaper(modelId);
        return success(true);
    }

    @PutMapping("/update")
    @Operation(summary = "更新测评模型")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-model:update')")
    public CommonResult<Boolean> updateAsmtModel(@Valid @RequestBody AsmtModelSaveReqVO updateReqVO) {
        asmtModelService.updateAsmtModel(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除测评模型")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hrm:asmt-model:delete')")
    public CommonResult<Boolean> deleteAsmtModel(@RequestParam("id") Long id) {
        asmtModelService.deleteAsmtModel(id);
        return success(true);
    }

    @GetMapping("/get-original-index")
    @Operation(summary = "获得测评模型关联的评测包所有试卷及其题目")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-model:query')")
    public CommonResult<Object> getModelOriginalIndex(@RequestParam("id") Long id) {
        return success(asmtModelService.getModelOriginalIndex(id));
    }

    @GetMapping("/get")
    @Operation(summary = "获得测评模型")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-model:query')")
    public CommonResult<AsmtModelRespVO> getAsmtModel(@RequestParam("id") Long id) {
        AsmtModelDO asmtModel = asmtModelService.getAsmtModel(id);
        return success(BeanUtils.toBean(asmtModel, AsmtModelRespVO.class));
    }

    @GetMapping("/get-valid-by-pkg")
    @Operation(summary = "获得测评模型")
    @Parameter(name = "pkgId", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-model:query')")
    public CommonResult<AsmtModelRespVO> getModel(@RequestParam("pkgId") Long pkgId) {
        AsmtModelDO asmtModel = asmtModelService.getValidModelByPkgId(pkgId);
        return success(BeanUtils.toBean(asmtModel, AsmtModelRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得测评模型分页")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-model:query')")
    public CommonResult<PageResult<AsmtModelRespVO>> getAsmtModelPage(@Valid AsmtModelPageReqVO pageReqVO) {
        PageResult<AsmtModelDO> pageResult = asmtModelService.getAsmtModelPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AsmtModelRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出测评模型 Excel")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-model:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAsmtModelExcel(@Valid AsmtModelPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AsmtModelDO> list = asmtModelService.getAsmtModelPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "测评模型.xls", "数据", AsmtModelRespVO.class,
                        BeanUtils.toBean(list, AsmtModelRespVO.class));
    }

    // ==================== 子表（测评指标） ====================

    @GetMapping("/asmt-index/page")
    @Operation(summary = "获得测评指标分页")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-model:query')")
    public CommonResult<PageResult<AsmtIndexDO>> getAsmtIndexPage(@Valid AsmtIndexPageReqVO pageReqVO) {
        return success(asmtModelService.getAsmtIndexPage(pageReqVO));
    }

    @GetMapping("/asmt-index/list")
    @Operation(summary = "获得测评指标列表")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-model:query')")
    public CommonResult<List<AsmtIndexDO>> getAsmtIndexList(@Valid AsmtIndexListReqVO reqVO) {
        return success(asmtModelService.getIndexList(reqVO));
    }

    @GetMapping("/asmt-index/groups")
    @Operation(summary = "获得指定模型里测评指标分组")
    @Parameter(name = "modelId", description = "模型id")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-model:query')")
    public CommonResult<List<Object>> getIndexGroupsByModelId(@RequestParam(required = false) Long modelId,
                                                              @RequestParam(required =false) Long paperId) {
        return success(asmtModelService.getIndexGroupsByModelIdOrPaperId(modelId, paperId));
    }

    @PostMapping("/asmt-index/create")
    @Operation(summary = "创建测评指标")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-model:create')")
    public CommonResult<Long> createAsmtIndex(@Valid @RequestBody AsmtIndexDO asmtIndex) {
        return success(asmtModelService.createAsmtIndex(asmtIndex));
    }

    @PutMapping("/asmt-index/update")
    @Operation(summary = "更新测评指标")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-model:update')")
    public CommonResult<Boolean> updateAsmtIndex(@Valid @RequestBody AsmtIndexDO asmtIndex) {
        asmtModelService.updateAsmtIndex(asmtIndex);
        return success(true);
    }

    @PutMapping("/asmt-index/update-rule")
    @Operation(summary = "更新测评指标规则")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-model:update')")
    public CommonResult<Boolean> updateIndexRule(@Valid @RequestBody AsmtIndexDO asmtIndex) {
        asmtModelService.updateIndexRule(asmtIndex);
        return success(true);
    }

    @PostMapping("/asmt-index/validate-rule")
    @Operation(summary = "验证指标规则")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-model:update')")
    public CommonResult<String> validateIndexRule(@Valid @RequestBody AsmtIndexDO asmtIndex) {
        return success(asmtModelService.validateIndexRule(asmtIndex));
    }

    @PutMapping("/asmt-index/update-sort")
    @Operation(summary = "更新指标排序")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-model:update')")
    public CommonResult<Boolean> updateIndexSort(@Valid @RequestBody List<AsmtIndexDO> list) {
        asmtModelService.updateIndexSort(list);
        return success(true);
    }

    @DeleteMapping("/asmt-index/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除测评指标")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-model:delete')")
    public CommonResult<Boolean> deleteAsmtIndex(@RequestParam("id") Long id) {
        asmtModelService.deleteAsmtIndex(id);
        return success(true);
    }

    @GetMapping("/asmt-index/get-below-index")
    @Operation(summary = "获取指定指标可以使用的指标数据（包括导入指标）")
    @Parameter(name = "id", description = "指标编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-model:query')")
    public CommonResult<Object> getCanUseIndex(@RequestParam("id") Long id) {
        return success(asmtModelService.getCanUseIndex(id));
    }

	@GetMapping("/asmt-index/get")
	@Operation(summary = "获得测评指标")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hrm:asmt-model:query')")
	public CommonResult<AsmtIndexDO> getAsmtIndex(@RequestParam("id") Long id) {
	    return success(asmtModelService.getAsmtIndex(id));
	}


    @GetMapping("/asmt-index/get-import-template")
    @Operation(summary = "获得外部指标导入模板")
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 手动创建导出 demo
        List<IndexImportExcelVO> list = Collections.singletonList(
                IndexImportExcelVO
                        .builder()
                        .score(4.5)
                        .stdScore(6.0)
                        .refValue("8")
                        .indexName("KPI完成度")
                        .indexCode("A")
                        .indexGroup("默认分组")
                        .build()
        );
        // 输出
        ExcelUtils.write(response, "外部指标导入模板.xls", "指标列表", IndexImportExcelVO.class, list);
    }

    @PostMapping("/asmt-index/import")
    @Operation(summary = "导入指标")
    @Parameters({
            @Parameter(name = "file", description = "Excel 文件", required = true),
            @Parameter(name = "modelId", description = "模型ID", example = "1"),
            @Parameter(name = "userId", description = "用户ID", example = "1"),
            @Parameter(name = "taskId", description = "任务ID", example = "1")
    })
    public CommonResult<Object> importExcel(@RequestParam("file") MultipartFile file,
                                            @RequestParam Long userId,
                                            @RequestParam Long taskId,
                                            @RequestParam Long modelId) throws Exception {
        List<IndexImportExcelVO> list = ExcelUtils.read(file, IndexImportExcelVO.class);
        return success(asmtModelService.importIndexList(list, modelId, taskId, userId));
    }

}

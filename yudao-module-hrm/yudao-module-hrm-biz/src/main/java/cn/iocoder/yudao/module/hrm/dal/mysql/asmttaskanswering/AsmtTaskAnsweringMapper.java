package cn.iocoder.yudao.module.hrm.dal.mysql.asmttaskanswering;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.hrm.controller.admin.taskanswering.vo.TaskAnsweringRespVO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.asmttaskanswering.AsmtTaskAnsweringDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 测评任务正在答题记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AsmtTaskAnsweringMapper extends BaseMapperX<AsmtTaskAnsweringDO> {

    @Select("SELECT a.id, a.create_time, a.paper_id, a.user_task_id, p.paper_name, p.paper_code, p.asmt_type, tu.task_id, u.nickname, u.position_series, u.job_number, u.job_level,u.id AS user_id " +
            "FROM hrm_asmt_task_answering a " +
            "INNER JOIN hrm_asmt_paper p ON a.paper_id=p.id " +
            "INNER JOIN hrm_asmt_task_user tu ON a.user_task_id=tu.id " +
            "INNER JOIN system_users u ON tu.user_id=u.id " +
            "WHERE a.deleted=0 ORDER BY a.create_time DESC")
    List<TaskAnsweringRespVO> getAnsweringList();

}

package cn.iocoder.yudao.module.hrm.controller.admin.model.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 指标分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AsmtIndexPageReqVO extends PageParam {

    private String indexName;
    private String indexGroup;
    private Long modelId;
    private Long paperId;
    private Integer level;
    private Boolean isImport;
    private Integer status;

}

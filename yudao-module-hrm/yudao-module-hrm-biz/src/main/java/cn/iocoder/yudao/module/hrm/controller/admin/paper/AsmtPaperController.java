package cn.iocoder.yudao.module.hrm.controller.admin.paper;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.hrm.controller.admin.paper.vo.AsmtPaperPageReqVO;
import cn.iocoder.yudao.module.hrm.controller.admin.paper.vo.AsmtPaperRespVO;
import cn.iocoder.yudao.module.hrm.controller.admin.paper.vo.AsmtPaperSaveReqVO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.paper.AsmtPaperDO;
import cn.iocoder.yudao.module.hrm.service.paper.AsmtPaperService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 测评卷")
@RestController
@RequestMapping("/hrm/asmt-paper")
@Validated
public class AsmtPaperController {

    @Resource
    private AsmtPaperService asmtPaperService;

    @PostMapping("/create")
    @Operation(summary = "创建测评卷")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-paper:create')")
    public CommonResult<Long> createAsmtPaper(@Valid @RequestBody AsmtPaperSaveReqVO createReqVO) {
        return success(asmtPaperService.createAsmtPaper(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新测评卷")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-paper:update')")
    public CommonResult<Boolean> updateAsmtPaper(@Valid @RequestBody AsmtPaperSaveReqVO updateReqVO) {
        asmtPaperService.updateAsmtPaper(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除测评卷")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hrm:asmt-paper:delete')")
    public CommonResult<Boolean> deleteAsmtPaper(@RequestParam("id") Long id) {
        asmtPaperService.deleteAsmtPaper(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得测评卷")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-paper:query')")
    public CommonResult<AsmtPaperRespVO> getAsmtPaper(@RequestParam("id") Long id) {
        AsmtPaperDO asmtPaper = asmtPaperService.getAsmtPaper(id);
        return success(BeanUtils.toBean(asmtPaper, AsmtPaperRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得测评卷分页")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-paper:query')")
    public CommonResult<PageResult<AsmtPaperRespVO>> getAsmtPaperPage(@Valid AsmtPaperPageReqVO pageReqVO) {
        PageResult<AsmtPaperDO> pageResult = asmtPaperService.getAsmtPaperPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AsmtPaperRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出测评卷 Excel")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-paper:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAsmtPaperExcel(@Valid AsmtPaperPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AsmtPaperDO> list = asmtPaperService.getAsmtPaperPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "测评卷.xls", "数据", AsmtPaperRespVO.class,
                        BeanUtils.toBean(list, AsmtPaperRespVO.class));
    }

}
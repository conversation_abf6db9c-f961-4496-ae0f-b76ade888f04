package cn.iocoder.yudao.module.hrm.dal.mysql.model;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.hrm.controller.admin.model.vo.AsmtIndexListReqVO;
import cn.iocoder.yudao.module.hrm.controller.admin.model.vo.AsmtIndexPageReqVO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.model.AsmtIndexDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 测评指标 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AsmtIndexMapper extends BaseMapperX<AsmtIndexDO> {

    default PageResult<AsmtIndexDO> selectPage(AsmtIndexPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AsmtIndexDO>()
                .likeIfPresent(AsmtIndexDO::getIndexName, reqVO.getIndexName())
                .eqIfPresent(AsmtIndexDO::getModelId, reqVO.getModelId())
                .eqIfPresent(AsmtIndexDO::getIndexGroup, reqVO.getIndexGroup())
                .eqIfPresent(AsmtIndexDO::getLevel, reqVO.getLevel())
                .eqIfPresent(AsmtIndexDO::getStatus, reqVO.getStatus())
                .eqIfPresent(AsmtIndexDO::getIsImport, reqVO.getIsImport())
                .orderByAsc(AsmtIndexDO::getLevel)
                .orderByAsc(AsmtIndexDO::getSort));
    }

    default List<AsmtIndexDO> getList(AsmtIndexListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AsmtIndexDO>()
                .likeIfPresent(AsmtIndexDO::getIndexName, reqVO.getIndexName())
                .eqIfPresent(AsmtIndexDO::getModelId, reqVO.getModelId())
                .eqIfPresent(AsmtIndexDO::getPaperId, reqVO.getPaperId())
                .eqIfPresent(AsmtIndexDO::getIndexGroup, reqVO.getIndexGroup())
                .eqIfPresent(AsmtIndexDO::getLevel, reqVO.getLevel())
                .eqIfPresent(AsmtIndexDO::getStatus, reqVO.getStatus())
                .eqIfPresent(AsmtIndexDO::getIsImport, reqVO.getIsImport())
                .orderByAsc(AsmtIndexDO::getLevel)
                .orderByAsc(AsmtIndexDO::getSort));
    }

    /**
     * 获取指定模型里低于指定指标级别的非导入正常指标
     * @param modelId  模型ID
     * @param level 级别
     * @return 结果
     */
    default List<AsmtIndexDO> getIndexListBelowLevel(Long modelId, Integer level) {
        return selectList(new LambdaQueryWrapperX<AsmtIndexDO>()
                .eq(AsmtIndexDO::getModelId, modelId)
                .lt(AsmtIndexDO::getLevel, level)
                .eq(AsmtIndexDO::getIsImport, false)
                .orderByAsc(AsmtIndexDO::getLevel)
                .orderByAsc(AsmtIndexDO::getSort));
    }

    default void deleteByModelId(Long modelId) {
        delete(AsmtIndexDO::getModelId, modelId);
    }

    default List<Map<String, Object>> getIndexGroupsByModelIdAndPaperId(Long modelId, Long paperId) {
        return selectMaps(
                new LambdaQueryWrapperX<AsmtIndexDO>()
                        .eqIfPresent(AsmtIndexDO::getPaperId, paperId)
                        .eqIfPresent(AsmtIndexDO::getModelId, modelId)
                        .isNotNull(AsmtIndexDO::getIndexGroup)
                        .select(AsmtIndexDO::getIndexGroup)
                        .groupBy(AsmtIndexDO::getIndexGroup)
        );
    }

    default AsmtIndexDO getByModelIdAndIndexName(Long modelId, String indexName) {
        return selectOne(AsmtIndexDO::getModelId, modelId, AsmtIndexDO::getIndexName, indexName);
    }
}

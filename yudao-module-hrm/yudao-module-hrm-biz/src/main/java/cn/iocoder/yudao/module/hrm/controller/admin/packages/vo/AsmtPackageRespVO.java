package cn.iocoder.yudao.module.hrm.controller.admin.packages.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 测评包 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AsmtPackageRespVO {

    @Schema(description = "标签id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31063")
    @ExcelProperty("标签id")
    private Long id;

    @Schema(description = "测评包名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("测评包名称")
    private String pkgName;

    @Schema(description = "是否随机题目排序")
    @ExcelProperty("是否随机题目排序")
    private Boolean quesRandom;

    @Schema(description = "标签")
    @ExcelProperty("标签")
    private String tags;

    @Schema(description = "描述", example = "你说的对")
    @ExcelProperty("描述")
    private String description;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "状态", example = "1")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat("common_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    private List<AsmtPackagePaperRespVO> paperList;
}

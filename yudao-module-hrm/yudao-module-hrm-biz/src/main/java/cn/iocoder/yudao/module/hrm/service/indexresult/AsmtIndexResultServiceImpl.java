package cn.iocoder.yudao.module.hrm.service.indexresult;

import cn.hutool.json.JSONObject;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.hrm.controller.admin.indexresult.vo.*;
import cn.iocoder.yudao.module.hrm.dal.dataobject.answersheet.AsmtAnswersheetDO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.indexresult.AsmtIndexResultDO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.model.AsmtIndexDO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.model.AsmtModelDO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.stdscore.AsmtIndexStdscoreDO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.task.AsmtTaskDO;
import cn.iocoder.yudao.module.hrm.dal.mysql.indexresult.AsmtIndexResultMapper;
import cn.iocoder.yudao.module.hrm.dal.mysql.model.AsmtIndexMapper;
import cn.iocoder.yudao.module.hrm.dal.mysql.model.AsmtModelMapper;
import cn.iocoder.yudao.module.hrm.dal.mysql.stdscore.AsmtIndexStdscoreMapper;
import cn.iocoder.yudao.module.hrm.dal.mysql.task.AsmtTaskMapper;
import cn.iocoder.yudao.module.hrm.service.answersheet.AsmtAnswersheetService;
import cn.iocoder.yudao.module.hrm.service.model.AsmtModelService;
import cn.iocoder.yudao.module.hrm.service.stdscore.AsmtIndexStdscoreService;
import cn.iocoder.yudao.module.hrm.service.task.AsmtTaskService;
import cn.iocoder.yudao.module.hrm.utils.AmstFormulaCalculator;
import cn.iocoder.yudao.module.system.api.dept.PostApi;
import cn.iocoder.yudao.module.system.api.dept.dto.PostRespDTO;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.hrm.enums.ErrorCodeConstants.*;

/**
 * 测评指标计算结果 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class AsmtIndexResultServiceImpl implements AsmtIndexResultService {

    @Resource
    private AsmtIndexResultMapper asmtIndexResultMapper;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private PostApi postApi;

    @Resource
    private AsmtTaskMapper asmtTaskMapper;

    @Resource
    private AsmtIndexMapper asmtIndexMapper;

    @Resource
    private AsmtModelMapper modelMapper;

    @Resource
    private AsmtTaskService asmtTaskService;

    @Resource
    private AsmtModelService asmtModelService;

    @Resource
    private AsmtAnswersheetService asmtAnswersheetService;

    @Resource
    private AsmtIndexStdscoreService asmtIndexStdscoreService;


    @Override
    public Long createAsmtIndexResult(AsmtIndexResultSaveReqVO createReqVO) {
        // 插入
        AsmtIndexResultDO asmtIndexResult = BeanUtils.toBean(createReqVO, AsmtIndexResultDO.class);
        asmtIndexResultMapper.insert(asmtIndexResult);
        // 返回
        return asmtIndexResult.getId();
    }

    @Override
    public void updateAsmtIndexResult(AsmtIndexResultSaveReqVO updateReqVO) {
        // 校验存在
        validateAsmtIndexResultExists(updateReqVO.getId());
        // 更新
        AsmtIndexResultDO updateObj = BeanUtils.toBean(updateReqVO, AsmtIndexResultDO.class);
        asmtIndexResultMapper.updateById(updateObj);
    }

    @Override
    public void deleteAsmtIndexResult(Long id) {
        // 校验存在
        validateAsmtIndexResultExists(id);
        // 删除
        asmtIndexResultMapper.deleteById(id);
    }

    @Override
    public void deleteAsmtIndexResultBatch(List<Long> ids) {
        asmtIndexResultMapper.deleteByIds(ids);
    }

    @Override
    public void deleteAsmtIndexResultByTaskIdAndUserId(Long taskId, Long userId) {
        asmtIndexResultMapper.deleteByTaskIdAndUserId(taskId, userId);
    }

    private void validateAsmtIndexResultExists(Long id) {
        if (asmtIndexResultMapper.selectById(id) == null) {
            throw exception(ASMT_INDEX_RESULT_NOT_EXISTS);
        }
    }

    @Override
    public AsmtIndexResultDO getAsmtIndexResult(Long id) {
        return asmtIndexResultMapper.selectById(id);
    }

    @Override
    public PageResult<AsmtIndexResultRespVO> getAsmtIndexResultPage(AsmtIndexResultPageReqVO pageReqVO) {
        PageResult<AsmtIndexResultDO> pageResult = asmtIndexResultMapper.selectPage(pageReqVO);
        PageResult<AsmtIndexResultRespVO> res = BeanUtils.toBean(pageResult, AsmtIndexResultRespVO.class);

        List<AsmtIndexResultRespVO> list = res.getList();
        if (!list.isEmpty()) {
            List<Long> userIds = list.stream().map(AsmtIndexResultRespVO::getUserId).toList();
            List<Long> taskIds = list.stream().map(AsmtIndexResultRespVO::getTaskId).toList();
            Map<Long, AdminUserRespDTO> userMap = adminUserApi.getUserList(userIds).stream().collect(Collectors.toMap(AdminUserRespDTO::getId, Function.identity()));
            Map<Long, AsmtTaskDO> taskMap = asmtTaskMapper.selectList(
                    new LambdaQueryWrapper<AsmtTaskDO>().in(AsmtTaskDO::getId, taskIds)
            ).stream().collect(Collectors.toMap(AsmtTaskDO::getId, Function.identity()));

            list.forEach(respVO -> {
                respVO.setUserName(userMap.get(respVO.getUserId()).getNickname());
                respVO.setJobNumber(userMap.get(respVO.getUserId()).getJobNumber());
                respVO.setTaskName(taskMap.get(respVO.getTaskId()).getTaskName());
            });
        }

        return res;
    }

    @Override
    public Map<String, Object> getUserIndexResultMap(Long taskId, Long modelId, Long userId, boolean isTile) {
        List<AsmtIndexResultDO> list = asmtIndexResultMapper.selectList(
                new LambdaQueryWrapperX<AsmtIndexResultDO>()
                        .eqIfPresent(AsmtIndexResultDO::getTaskId, taskId)
                        .eqIfPresent(AsmtIndexResultDO::getModelId, modelId)
                        .eqIfPresent(AsmtIndexResultDO::getUserId, userId)
        );

        if (!list.isEmpty()) {
            List<AsmtIndexResultRespVO> list1 = BeanUtils.toBean(list, AsmtIndexResultRespVO.class);
            List<Long> indexIds = list1.stream().map(AsmtIndexResultRespVO::getIndexId).toList();
            Map<Long, AsmtIndexDO> indexMap = asmtIndexMapper.selectList(
                    new LambdaQueryWrapper<AsmtIndexDO>().in(AsmtIndexDO::getId, indexIds)
            ).stream().collect(Collectors.toMap(AsmtIndexDO::getId, Function.identity()));

            Map<String, Object> result = new HashMap<>();
            for (AsmtIndexResultRespVO respVO : list1) {
                respVO.setIndexName(indexMap.get(respVO.getIndexId()).getIndexName());
                String k = "index_" + respVO.getIndexId();
                if (respVO.getIndexCode() != null) {
                    k = "index_" + respVO.getIndexCode();
                }
                if (isTile) {
                    result.put(k + "_score", respVO.getScore());
                    result.put(k + "_stdScore", respVO.getStdScore());
                    result.put(k + "_refValue", respVO.getRefValue());
                } else {
                    result.put(k, respVO);
                }
            }

            AdminUserRespDTO user = adminUserApi.getUser(userId);
            if (user.getPostIds() != null && !user.getPostIds().isEmpty()) {
                List<PostRespDTO> postRespDTOS = postApi.getPostList(user.getPostIds());
                user.setPostNames(postRespDTOS.stream().map(PostRespDTO::getName).collect(Collectors.toSet()));
            }
            AsmtTaskDO task = asmtTaskMapper.selectById(taskId);
            AsmtModelDO model = modelMapper.selectById(modelId);
            if (isTile) {
                result.put("userName", user.getNickname());
                result.put("userAvatar", user.getAvatar());
                result.put("userMobile", user.getMobile());

                result.put("taskName", task.getTaskName());

                result.put("modelName", model.getModelName());
            } else {
                result.put("user", user);
                result.put("task", task);
                result.put("model", model);
            }

            return result;
        }
        return null;
    }


    @Override
    public Object calcIndexResult(Long taskId, Long userId) {
        AsmtTaskDO taskDO = asmtTaskService.getAsmtTask(taskId);
        if (taskDO == null) {
            throw exception(ASMT_TASK_NOT_EXISTS);
        }

        //获取测评模型
        AsmtModelDO modelDO = asmtModelService.getAsmtModelByPkgId(taskDO.getPkgId());
        if (modelDO == null) {
            throw exception(ASMT_MODEL_NOT_MATCH);
        }

        //删除上次测评结果（除了导入指标）
        log.info("删除上次测评结果...");
        this.deleteAsmtIndexResultByTaskIdAndUserId(taskId, userId);

        //计算公式的变量
        Map<String, Double> variables = new HashMap<>();

        //获取答题结果转为变量
        procAnswerSheetToVariables(taskId, userId, variables);

        //导入指标数据转为变量
        procImportIndexToVariables(taskId, userId, variables);

        //获取需计算的指标定义，计算指标(遍历指标，先计算低级别指标)
        List<AsmtIndexDO> indexList = asmtModelService.getCalcIndexList(modelDO.getId());
        for (AsmtIndexDO index : indexList) {
            log.info("开始计算指标：{} - {}，计算公式：{}", index.getIndexCode(), index.getIndexName(), index.getRule());

            //计算指标值
            AsmtIndexResultSaveReqVO resultVO = new AsmtIndexResultSaveReqVO();
            resultVO.setModelId(modelDO.getId())
                    .setIndexId(index.getId())
                    .setIndexCode(index.getIndexCode())
                    .setIndexName(index.getIndexName())
                    .setIndexGroup(index.getIndexGroup())
                    .setUserId(userId)
                    .setTaskId(taskId)
                    .setCoverType(index.getCoverType())
                    .setIsImport(false);

            String rule = index.getRule();
            //计算公式为空则跳过
            if (StringUtils.isBlank(rule)) {
                resultVO.setDescription("指标计算公式为空");
            } else {
                try {
                    double value = AmstFormulaCalculator.calculate(rule, variables);
                    resultVO.setScore(value);

                    //保存计算原始分到变量中，先保存是因为可能在在计算标准分时用到；兼容通过指标id和code检索
                    variables.put("index.id." + index.getId(), resultVO.getScore());
                    variables.put("index.code." + index.getIndexCode(), resultVO.getScore());

                    //处理特殊指标的标准分
                    int coverType = (index.getCoverType() != null) ? index.getCoverType() : 0;
                    if (coverType == 1) {
                        resultVO.setStdScore(coverToStdScore(modelDO.getId(), index.getIndexCode(), value));
                    } else if (coverType == 2) {
                        String coverRule = index.getCoverRule();
                        if (StringUtils.isBlank(coverRule)) {
                            resultVO.setDescription("指标转换公式为空");
                        }
                        double stdScore = AmstFormulaCalculator.calculate(coverRule, variables);
                        resultVO.setStdScore(stdScore);
                    }
                    //保存标准分到变量中，变量后缀为std，兼容通过指标id和code检索
                    variables.put("index.id." + index.getIndexCode() + ".std", resultVO.getStdScore());
                    variables.put("index.code." + index.getIndexCode() + ".std", resultVO.getStdScore());
                } catch (Exception e) {
                    log.warn("指标计算错误：" + e.getMessage());
                    resultVO.setDescription("指标计算公式错误：" + e.getMessage());
                }
            }

            //保存指标结果
            this.createAsmtIndexResult(resultVO);
        }

        return "计算完成";
    }

    /**
     * 处理答题结果转为变量
     *
     * @param taskId
     * @param userId
     * @param variables
     */
    private void procAnswerSheetToVariables(Long taskId, Long userId, Map<String, Double> variables) {
        List<AsmtAnswersheetDO> list = asmtAnswersheetService.selectListByTaskUser(taskId, userId);
        log.info("获取到答题结果数据{}条", list.size());
        for (AsmtAnswersheetDO answersheet : list) {
            String key = "answer." + answersheet.getPaperId() + "." + answersheet.getQuesSort();
            Double score = Double.valueOf(0);
            //如果answersheet.getScore() 为null 则默认为0
            if (answersheet.getScore() != null) {
                score = answersheet.getScore();
            }
            variables.put(key, score);
        }
    }

    /**
     * 处理导入指标数据转为变量
     *
     * @param taskId
     * @param userId
     * @param variables
     */
    private void procImportIndexToVariables(Long taskId, Long userId, Map<String, Double> variables) {
        List<AsmtIndexResultDO> list = asmtIndexResultMapper.selectImportListByTaskUser(taskId, userId);
        log.info("获取到导入指标数据{}条", list.size());
        for (AsmtIndexResultDO indexResult : list) {
            variables.put("index.id." + indexResult.getIndexId(), indexResult.getScore());
            variables.put("index.code." + indexResult.getIndexCode(), indexResult.getScore());
            variables.put("index.id." + indexResult.getIndexId() + ".std", indexResult.getStdScore());
        }
    }


    @Override
    public double coverToStdScore(Long modelId, String indexCode, double origin) {
        double stdScore = 0;

        //获取该指标转换数据并判断原始分在哪个区间
        List<AsmtIndexStdscoreDO> listIndexStd = asmtIndexStdscoreService.getAsmtIndexStdscoreList(indexCode);
        for (AsmtIndexStdscoreDO aDo : listIndexStd) {
            if (origin >= aDo.getRawMin() && origin <= aDo.getRawMax()) {
                stdScore = aDo.getStdScore();
                break;
            }
        }

        log.info("指标{}，原始分：{} 标准分：{}", indexCode, origin, stdScore);
        return stdScore;
    }

    @Override
    public void exportTableDataByTaskUser(Long taskId, Long userId, HttpServletResponse response) throws IOException {
        List<IndexResultTableExportResVO> list = asmtIndexResultMapper.getExportTableListByTaskUser(taskId, userId);
        // 获取表头
        Map<String, IndexResultTableHerderVO> headerMap = new HashMap<>();
        list.forEach(item -> headerMap.putIfAbsent(item.getKeyId(), BeanUtils.toBean(item, IndexResultTableHerderVO.class)));

        // 获取以用户ID为
        Map<String, List<IndexResultTableExportResVO>> userMap = list.stream().collect(Collectors.groupingBy(IndexResultTableExportResVO::getUserKey));
        List<List<IndexResultTableExportResVO>> userList = new ArrayList<>(userMap.values());

        List<String> keys = list.stream().map(IndexResultTableExportResVO::getKeyId).distinct().toList();
        HSSFWorkbook wb = new HSSFWorkbook();// 创建HSSFWorkbook对象
        HSSFSheet sheet = wb.createSheet("Sheet1");//建立sheet对象

        CellStyle centerStyle = wb.createCellStyle();

        // 设置居中
        centerStyle.setAlignment(HorizontalAlignment.CENTER);  // 左右居中
        centerStyle.setVerticalAlignment(VerticalAlignment.CENTER);  // 垂直居中
        // 设置细线边框（四边）
        centerStyle.setBorderTop(BorderStyle.THIN);
        centerStyle.setBorderBottom(BorderStyle.THIN);
        centerStyle.setBorderLeft(BorderStyle.THIN);
        centerStyle.setBorderRight(BorderStyle.THIN);

        // 创建表头
        HSSFRow row0 = sheet.createRow(0);
        createCell(row0,0, centerStyle).setCellValue("姓名");
        createCell(row0,1, centerStyle).setCellValue("工号");
        createCell(row0,2, centerStyle).setCellValue("岗位序列");
        createCell(row0,3, centerStyle).setCellValue("职级");

        HSSFRow row1 = sheet.createRow(1);

        // 创建合并区域，参数依次为：起始行、结束行、起始列、结束列
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 0)); // 姓名
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 1, 1)); // 工号
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 2, 2)); // 岗位序列
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 3, 3)); // 职级

        // 创建指标相关表头
        int i = 4;
        String group = null;
        int startCol = i;// 开始合并表头的列索引
        for (String key : keys) {
            IndexResultTableHerderVO header = headerMap.get(key);
            createCell(row0,i, centerStyle).setCellValue(header.getIndexGroup());
            createCell(row1,i, centerStyle).setCellValue(header.getIndexName());

            if (group == null) {
                group = header.getIndexGroup();
            } else if (!header.getIndexGroup().equals(group)) {
                // 创建合并区域，参数依次为：起始行、结束行、起始列、结束列
                if (i - startCol >= 2) {
                    sheet.addMergedRegion(new CellRangeAddress(0, 0, startCol, i - 1)); // 姓名
                }
                startCol = i;
                group = header.getIndexGroup();
            }

            i++;
//            i++;
//            row1.createCell(i).setCellValue(header.getIndexName() + "标准分");
        }

        int r = 2; // 从第3行开始
        for (List<IndexResultTableExportResVO> vos : userList) {
            HSSFRow row = sheet.createRow(r);
            IndexResultTableExportResVO first = vos.get(0);

            createCell(row,0, centerStyle).setCellValue(first.getNickname());
            createCell(row,1, centerStyle).setCellValue(first.getJobNumber());
            createCell(row,2, centerStyle).setCellValue(first.getPositionSeries());
            createCell(row,3, centerStyle).setCellValue(first.getJobLevel());

            Map<String, Double> tempMap = new HashMap<>();
            for (IndexResultTableExportResVO vo : vos) {
                Double score = vo.getStdScore();
                if (vo.getCoverType() == null || vo.getCoverType() != 1) {
                    // 空或者0代表无标准分，则取原始分
                    score = vo.getScore();
                }
                tempMap.put(vo.getKeyId(), score);
//                tempMap.put(vo.getKeyId(), vo.getScore()); // 原始值
//                tempMap.put(vo.getKeyId() + "Std", vo.getStdScore());
            }

            i = 3;
            for (String key : keys) {
                Double score = tempMap.get(key);
//                Double stdScore = tempMap.get(key + "Std");

                i++;
                HSSFCell cell = createCell(row,i, centerStyle); // 原始分
                if (score != null) cell.setCellValue(score);
//                i++;
//                HSSFCell cell1 = row.createCell(i); // 标准分
//                if (stdScore != null) cell1.setCellValue(stdScore);
            }

            r++;
        }


        //输出Excel文件
        String fileName = "测评结果数据导出";
        OutputStream output = response.getOutputStream();
        response.reset();
        //设置响应头，
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8) + ".xls");
        response.setContentType("application/vnd.ms-excel");
        wb.write(output);
        output.close();
        wb.close();
    }

    private HSSFCell createCell(HSSFRow row, int column, CellStyle centerStyle) {
        HSSFCell cell = row.createCell(column);
        cell.setCellStyle(centerStyle);
        return cell;
    }

    private static JSONObject getUserObj(List<IndexResultTableExportResVO> vos) {
        IndexResultTableExportResVO first = vos.get(0);
        JSONObject object = new JSONObject();
        object.set("nickname", first.getNickname());
        object.set("positionSeries", first.getPositionSeries());
        object.set("jobLevel", first.getJobLevel());
        object.set("jobNumber", first.getJobNumber());
        object.set("jobTitle", first.getJobTitle());
        object.set("deptId", first.getDeptId());

        for (IndexResultTableExportResVO vo : vos) {
            object.set(vo.getKeyId(), vo.getScore()); // 原始值
            object.set(vo.getKeyId() + "Std", vo.getStdScore());
        }
        return object;
    }
}

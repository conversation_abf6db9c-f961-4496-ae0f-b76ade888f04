package cn.iocoder.yudao.module.hrm.dal.dataobject.asmttaskanswering;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 测评任务正在答题记录 DO
 *
 * <AUTHOR>
 */
@TableName("hrm_asmt_task_answering")
@KeySequence("hrm_asmt_task_answering_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AsmtTaskAnsweringDO extends BaseDO {

    /**
     * 标签id
     */
    @TableId
    private Long id;
    /**
     * 测评任务关联表id
     */
    private Long userTaskId;
    /**
     * 试卷ID
     */
    private Long paperId;

}
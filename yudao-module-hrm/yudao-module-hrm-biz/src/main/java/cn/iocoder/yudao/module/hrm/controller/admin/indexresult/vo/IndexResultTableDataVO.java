package cn.iocoder.yudao.module.hrm.controller.admin.indexresult.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 测评指标计算结果 Response VO")
@Data
public class IndexResultTableDataVO {

    @Schema(description = "指标编码")
    private String indexCode;

    @Schema(description = "指标分组")
    private String indexGroup;

    @Schema(description = "指标名称")
    private String indexName;

    @Schema(description = "KEY ID")
    private String keyId;

}

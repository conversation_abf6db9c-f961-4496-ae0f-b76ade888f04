package cn.iocoder.yudao.module.hrm.controller.admin.benchmark.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 序列岗位指标基准 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AsmtIndexBenchmarkRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "9741")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "系列")
    @ExcelProperty(value = "系列", converter = DictConvert.class)
    @DictFormat("hrm_position_series") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private String series;

    @Schema(description = "层级")
    @ExcelProperty("层级")
    private String level;

    @Schema(description = "固有聪慧")
    @ExcelProperty("固有聪慧")
    private Double hx1;
    private Double hx1Weight;

    @Schema(description = "科学认知")
    @ExcelProperty("科学认知")
    private Double hx2;
    private Double hx2Weight;

    @Schema(description = "反省利他")
    @ExcelProperty("反省利他")
    private Double hx3;
    private Double hx3Weight;

    @Schema(description = "AI思维")
    @ExcelProperty("AI思维")
    private Double hx4;
    private Double hx4Weight;

    @Schema(description = "乐群凝聚")
    @ExcelProperty("乐群凝聚")
    private Double hx5;
    private Double hx5Weight;

    @Schema(description = "魄力果敢")
    @ExcelProperty("魄力果敢")
    private Double hx6;
    private Double hx6Weight;

    @Schema(description = "系统思维")
    @ExcelProperty("系统思维")
    private Double hx7;
    private Double hx7Weight;

    @Schema(description = "专业特质")
    @ExcelProperty("专业特质")
    private Double hx8;
    private Double hx8Weight;

    @Schema(description = "深度思考")
    @ExcelProperty("深度思考")
    private Double hx9;
    private Double hx9Weight;

    @Schema(description = "简单执着")
    @ExcelProperty("简单执着")
    private Double hx10;
    private Double hx10Weight;

    @Schema(description = "创新突破")
    @ExcelProperty("创新突破")
    private Double hx11;
    private Double hx11Weight;

    @Schema(description = "业绩证明")
    @ExcelProperty("业绩证明")
    private Double hx12;
    private Double hx12Weight;

    @Schema(description = "层次均值")
    @ExcelProperty("层次均值")
    private Double levelAverage;

}

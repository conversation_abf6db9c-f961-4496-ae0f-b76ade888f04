package cn.iocoder.yudao.module.hrm.service.question;

import cn.hutool.json.JSONObject;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.SortingField;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.hrm.controller.admin.question.vo.AsmtQuestionEditReqVO;
import cn.iocoder.yudao.module.hrm.controller.admin.question.vo.AsmtQuestionOptionSaveReqVO;
import cn.iocoder.yudao.module.hrm.controller.admin.question.vo.AsmtQuestionPageReqVO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.question.AsmtQuestionDO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.question.AsmtQuestionOptionDO;
import cn.iocoder.yudao.module.hrm.dal.mysql.question.AsmtQuestionMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.hrm.enums.ErrorCodeConstants.ASMT_QUESTION_NOT_EXISTS;

/**
 * 测评题 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AsmtQuestionServiceImpl implements AsmtQuestionService {

    @Resource
    private AsmtQuestionMapper asmtQuestionMapper;

    @Resource
    private AsmtQuestionOptionService questionOptionService;

    @Override
    public Long createAsmtQuestion(AsmtQuestionEditReqVO reqVO) {
        // 插入
        AsmtQuestionDO asmtQuestion = BeanUtils.toBean(reqVO.getQuestion(), AsmtQuestionDO.class);
        asmtQuestionMapper.insert(asmtQuestion);

        // 插入答案选项
        if (reqVO.getOptions() != null && !reqVO.getOptions().isEmpty()) {
            int sort = 0;
            for (AsmtQuestionOptionSaveReqVO option : reqVO.getOptions()) {
                option.setQuseId(asmtQuestion.getId());
                option.setSort(sort++);
                questionOptionService.createAsmtQuestionOption(option);
            }
        }

        // 返回
        return asmtQuestion.getId();
    }

    @Override
    public void updateAsmtQuestion(AsmtQuestionEditReqVO updateReqVO) {
        // 校验存在
        validateAsmtQuestionExists(updateReqVO.getQuestion().getId());
        // 更新题目
        AsmtQuestionDO updateObj = BeanUtils.toBean(updateReqVO.getQuestion(), AsmtQuestionDO.class);
        asmtQuestionMapper.updateById(updateObj);

        // 更新选项
        if (updateReqVO.getOptions() != null && !updateReqVO.getOptions().isEmpty()) {
            int sort = 0;
            for (AsmtQuestionOptionSaveReqVO option : updateReqVO.getOptions()) {
                option.setQuseId(updateObj.getId());
                option.setSort(sort++);

                if (option.getId() == null) {
                    questionOptionService.createAsmtQuestionOption(option);
                }else {
                    questionOptionService.updateAsmtQuestionOption(option);
                }
            }
        }

    }

    @Override
    public void deleteAsmtQuestion(Long id) {
        // 校验存在
        validateAsmtQuestionExists(id);
        // 删除
        asmtQuestionMapper.deleteById(id);
    }

    private void validateAsmtQuestionExists(Long id) {
        if (asmtQuestionMapper.selectById(id) == null) {
            throw exception(ASMT_QUESTION_NOT_EXISTS);
        }
    }

    @Override
    public AsmtQuestionDO getAsmtQuestion(Long id) {
        return asmtQuestionMapper.selectById(id);
    }

    @Override
    public JSONObject getAsmtQuestionDetail(Long id) {
        AsmtQuestionDO asmtQuestion = asmtQuestionMapper.selectById(id);
        List<AsmtQuestionOptionDO> optionDOList = questionOptionService.getAsmtQuestionOptionList(id);
        JSONObject res = new JSONObject();
        res.set("question", asmtQuestion);
        res.set("options", optionDOList);

        return res;
    }

    @Override
    public PageResult<AsmtQuestionDO> getAsmtQuestionPage(AsmtQuestionPageReqVO pageReqVO) {
        return asmtQuestionMapper.selectPage(pageReqVO);
    }

    @Override
    /**
     * 获取测评题分页，默认id升序
     *
     * @param pageReqVO
     * @return
     */
    public PageResult<AsmtQuestionDO> getAsmtQuestionPageOrderById(AsmtQuestionPageReqVO pageReqVO) {
        SortingField sf = new SortingField("id", SortingField.ORDER_ASC);
        pageReqVO.setSortingFields(new ArrayList<SortingField>() {{
            add(sf);
        }});
        return getAsmtQuestionPage(pageReqVO);
    }
}

package cn.iocoder.yudao.module.hrm.controller.admin.indexresult.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测评指标计算结果 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AsmtIndexResultRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "22258")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1912")
    @ExcelProperty("用户id")
    private Long userId;

    @Schema(description = "用户姓名")
    @ExcelProperty("姓名")
    private String userName;

    @Schema(description = "工号")
    @ExcelProperty("工号")
    private String jobNumber;

    @Schema(description = "任务id", example = "18350")
    @ExcelProperty("任务id")
    private Long taskId;

    @Schema(description = "任务名称")
    @ExcelProperty("任务名称")
    private String taskName;

    @Schema(description = "模型id", example = "11746")
    @ExcelProperty("模型id")
    private Long modelId;

    @Schema(description = "指标编码")
    @ExcelProperty("指标编码")
    private String indexCode;

    /**
     * 组名
     */
    @Schema(description = "指标分组")
    @ExcelProperty("指标分组")
    private String indexGroup;

    @Schema(description = "指标id", example = "31228")
    @ExcelProperty("指标id")
    private Long indexId;

    @Schema(description = "指标名称")
    @ExcelProperty("指标名称")
    private String indexName;

    @Schema(description = "得分")
    @ExcelProperty("得分")
    private Double score;

    @Schema(description = "标准分转换方式")
    private Integer coverType;

    @Schema(description = "标准分")
    @ExcelProperty("标准分")
    private Double stdScore;

    @Schema(description = "参考值")
    @ExcelProperty("参考值")
    private String refValue;

    @Schema(description = "更新时间")
    @ExcelProperty("更新时间")
    private LocalDateTime updateTime;

}

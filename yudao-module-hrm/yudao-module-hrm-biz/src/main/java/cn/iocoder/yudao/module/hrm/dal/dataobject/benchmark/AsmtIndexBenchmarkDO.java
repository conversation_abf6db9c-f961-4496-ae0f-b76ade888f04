package cn.iocoder.yudao.module.hrm.dal.dataobject.benchmark;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 序列岗位指标基准 DO
 *
 * <AUTHOR>
 */
@TableName("hrm_asmt_index_benchmark")
@KeySequence("hrm_asmt_index_benchmark_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AsmtIndexBenchmarkDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 系列
     *
     * 枚举 {@link TODO hrm_position_series 对应的类}
     */
    private String series;
    /**
     * 层级
     */
    private String level;
    /**
     * 固有聪慧
     */
    private Double hx1;
    private Double hx1Weight;
    /**
     * 科学认知
     */
    private Double hx2;
    private Double hx2Weight;
    /**
     * 反省利他
     */
    private Double hx3;
    private Double hx3Weight;
    /**
     * AI思维
     */
    private Double hx4;
    private Double hx4Weight;
    /**
     * 乐群凝聚
     */
    private Double hx5;
    private Double hx5Weight;
    /**
     * 魄力果敢
     */
    private Double hx6;
    private Double hx6Weight;
    /**
     * 系统思维
     */
    private Double hx7;
    private Double hx7Weight;
    /**
     * 专业特质
     */
    private Double hx8;
    private Double hx8Weight;
    /**
     * 深度思考
     */
    private Double hx9;
    private Double hx9Weight;
    /**
     * 简单执着
     */
    private Double hx10;
    private Double hx10Weight;
    /**
     * 创新突破
     */
    private Double hx11;
    private Double hx11Weight;
    /**
     * 业绩证明
     */
    private Double hx12;
    private Double hx12Weight;
    /**
     * 层次均值
     */
    private Double levelAverage;

}

package cn.iocoder.yudao.module.hrm.service.paper;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.hrm.controller.admin.paper.vo.AsmtPaperQuestionPageReqVO;
import cn.iocoder.yudao.module.hrm.controller.admin.paper.vo.AsmtPaperQuestionRespVO;
import cn.iocoder.yudao.module.hrm.controller.admin.paper.vo.AsmtPaperQuestionSaveReqVO;
import cn.iocoder.yudao.module.hrm.controller.admin.paper.vo.AsmtPaperQuestionSortReqVO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.paper.AsmtPaperQuestionDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 测评卷与测评题关联 Service 接口
 *
 * <AUTHOR>
 */
public interface AsmtPaperQuestionService {

    /**
     * 创建测评卷与测评题关联
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAsmtPaperQuestion(@Valid AsmtPaperQuestionSaveReqVO createReqVO);
    void createAsmtPaperQuestionBatch(@Valid List<AsmtPaperQuestionSaveReqVO> list);

    /**
     * 更新测评卷与测评题关联
     *
     * @param updateReqVO 更新信息
     */
    void updateAsmtPaperQuestion(@Valid AsmtPaperQuestionSaveReqVO updateReqVO);
    void updateAsmtPaperQuestionSort(@Valid List<AsmtPaperQuestionSortReqVO> list);

    /**
     * 删除测评卷与测评题关联
     *
     * @param id 编号
     */
    void deleteAsmtPaperQuestion(Long id);

    /**
     * 获得测评卷与测评题关联
     *
     * @param id 编号
     * @return 测评卷与测评题关联
     */
    AsmtPaperQuestionDO getAsmtPaperQuestion(Long id);

    /**
     * 根据试卷ID获取测评卷与测评题关联列表
     *
     * @param paperId 试卷ID
     * @return 关联关系列表
     */
    List<AsmtPaperQuestionRespVO> getAsmtPaperQuestionByPaperId(Long paperId);
    Object getPaperQuestionAndOptionsByPaperId(Long paperId);

    /**
     * 获得测评卷与测评题关联分页
     *
     * @param pageReqVO 分页查询
     * @return 测评卷与测评题关联分页
     */
    PageResult<AsmtPaperQuestionDO> getAsmtPaperQuestionPage(AsmtPaperQuestionPageReqVO pageReqVO);

}

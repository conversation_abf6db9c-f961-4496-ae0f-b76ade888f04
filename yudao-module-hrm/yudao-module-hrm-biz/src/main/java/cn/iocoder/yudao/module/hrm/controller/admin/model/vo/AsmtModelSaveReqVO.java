package cn.iocoder.yudao.module.hrm.controller.admin.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 测评模型新增/修改 Request VO")
@Data
public class AsmtModelSaveReqVO {

    @Schema(description = "标签id", requiredMode = Schema.RequiredMode.REQUIRED, example = "23937")
    private Long id;

    @Schema(description = "模型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "模型名称不能为空")
    private String modelName;

    @Schema(description = "测评包id", requiredMode = Schema.RequiredMode.REQUIRED, example = "24722")
    @NotNull(message = "测评包id不能为空")
    private Long pkgId;

    @Schema(description = "说明", example = "随便")
    private String description;

    @Schema(description = "测评报告模板", example = "1")
    private String tempCode;

    @Schema(description = "否决项指标id")
    private Long disIndex;

    @Schema(description = "状态", example = "1")
    private Integer status;

}

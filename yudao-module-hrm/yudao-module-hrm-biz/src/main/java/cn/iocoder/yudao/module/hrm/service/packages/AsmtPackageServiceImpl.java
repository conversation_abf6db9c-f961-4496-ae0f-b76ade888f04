package cn.iocoder.yudao.module.hrm.service.packages;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.hrm.controller.admin.packages.vo.AsmtPackagePageReqVO;
import cn.iocoder.yudao.module.hrm.controller.admin.packages.vo.AsmtPackagePaperSaveReqVO;
import cn.iocoder.yudao.module.hrm.controller.admin.packages.vo.AsmtPackageRespVO;
import cn.iocoder.yudao.module.hrm.controller.admin.packages.vo.AsmtPackageSaveReqVO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.packages.AsmtPackageDO;
import cn.iocoder.yudao.module.hrm.dal.mysql.packages.AsmtPackageMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.hrm.enums.ErrorCodeConstants.ASMT_PACKAGE_NOT_EXISTS;

/**
 * 测评包 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class AsmtPackageServiceImpl implements AsmtPackageService {

    @Resource
    private AsmtPackageMapper asmtPackageMapper;

    @Resource
    private AsmtPackagePaperService asmtPackagePaperService;

    @Override
    public Long createAsmtPackage(AsmtPackageSaveReqVO createReqVO) {
        // 插入
        AsmtPackageDO asmtPackage = BeanUtils.toBean(createReqVO, AsmtPackageDO.class);
        asmtPackageMapper.insert(asmtPackage);

        editPaper(createReqVO, asmtPackage.getId());
        // 返回
        return asmtPackage.getId();
    }

    @Override
    public void updateAsmtPackage(AsmtPackageSaveReqVO updateReqVO) {
        // 校验存在
        validateAsmtPackageExists(updateReqVO.getId());
        // 更新
        AsmtPackageDO updateObj = BeanUtils.toBean(updateReqVO, AsmtPackageDO.class);
        asmtPackageMapper.updateById(updateObj);

        editPaper(updateReqVO, updateObj.getId());
    }

    private void editPaper(AsmtPackageSaveReqVO reqVO, Long id) {
        // 添加测试卷
        if (reqVO.getPaperIds() != null && !reqVO.getPaperIds().isEmpty()) {
            int sort = 0;
            for (Long paperId : reqVO.getPaperIds()) {
                AsmtPackagePaperSaveReqVO vo = new AsmtPackagePaperSaveReqVO();
                vo.setPaperId(paperId);
                vo.setPkgId(id);
                vo.setSort(sort++);
                asmtPackagePaperService.createAsmtPackagePaper(vo);
            }
        }

        // 删除已关联的测试卷
        if (reqVO.getDelRelIds() != null && !reqVO.getDelRelIds().isEmpty()) {
            for (Long delRelId : reqVO.getDelRelIds()) {
                try {
                    asmtPackagePaperService.deleteAsmtPackagePaper(delRelId);
                }finally {
                    log.info("关联关系删除失败[{}]", delRelId);
                }
            }
        }
    }

    @Override
    public void deleteAsmtPackage(Long id) {
        // 校验存在
        validateAsmtPackageExists(id);
        // 删除
        asmtPackageMapper.deleteById(id);
    }

    private void validateAsmtPackageExists(Long id) {
        if (asmtPackageMapper.selectById(id) == null) {
            throw exception(ASMT_PACKAGE_NOT_EXISTS);
        }
    }

    @Override
    public AsmtPackageDO getAsmtPackage(Long id) {
        return asmtPackageMapper.selectById(id);
    }

    @Override
    public PageResult<AsmtPackageDO> getAsmtPackagePage(AsmtPackagePageReqVO pageReqVO) {
        return asmtPackageMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<AsmtPackageRespVO> getAsmtPackagePageWithPaper(AsmtPackagePageReqVO pageReqVO) {
        PageResult<AsmtPackageDO> pageResult = asmtPackageMapper.selectPage(pageReqVO);
        PageResult<AsmtPackageRespVO> pageResult1 = BeanUtils.toBean(pageResult, AsmtPackageRespVO.class);
        List<AsmtPackageRespVO> voList = pageResult1.getList();
        for (AsmtPackageRespVO vo : voList) {
            vo.setPaperList(asmtPackagePaperService.getListByPkgId(vo.getId()));
        }

        return pageResult1;
    }

}

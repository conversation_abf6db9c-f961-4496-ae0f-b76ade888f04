package cn.iocoder.yudao.module.hrm.controller.admin.answersheet.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 用户答题卡分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AsmtAnswersheetPageReqVO extends PageParam {

    @Schema(description = "用户id", example = "17769")
    private Long userId;

    @Schema(description = "测评任务id", example = "20312")
    private Long taskId;

    @Schema(description = "测试包id", example = "17698")
    private Long pkgId;

    @Schema(description = "测试卷id", example = "29248")
    private Long paperId;

    @Schema(description = "题目id", example = "27673")
    private Long quesId;

}

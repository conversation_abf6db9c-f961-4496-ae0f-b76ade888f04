package cn.iocoder.yudao.module.hrm.dal.dataobject.indexresult;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 测评指标计算结果 DO
 *
 * <AUTHOR>
 */
@TableName("hrm_asmt_index_result")
@KeySequence("hrm_asmt_index_result_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AsmtIndexResultDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 任务id
     */
    private Long taskId;
    /**
     * 模型id
     */
    private Long modelId;

    /**
     * 指标Code
     */
    private String indexCode;
    /**
     * 指标名称
     */
    private String indexName;

    /**
     * 指标分组
     */
    private String indexGroup;
    /**
     * 指标id
     */
    private Long indexId;
    /**
     * 得分
     */
    private Double score;
    /**
     * 标准分
     */
    private Double stdScore;
    /**
     * 参考值
     */
    private String refValue;
    /**
     * 是否为导入数据
     */
    private Boolean isImport;
    /**
     * 结果描述
     */
    private String description;

    /**
     * 标准分转换方式（0-不需要，1-区间映射表，2-公式）
     */
    private Integer coverType;

}
